Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker1.log
-srvPort
50427
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 161.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56316
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.013194 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
InvalidOperationException: Cannot process request because the process (7912) has exited.
  at System.Diagnostics.Process.GetProcessHandle (System.Int32 access, System.Boolean throwIfExited) [0x0004e] in <26e428272c1d4dd18c67cb8f0ee16c2e>:0 
  at System.Diagnostics.Process.GetProcessHandle (System.Int32 access) [0x00000] in <26e428272c1d4dd18c67cb8f0ee16c2e>:0 
  at System.Diagnostics.Process.Kill () [0x00002] in <26e428272c1d4dd18c67cb8f0ee16c2e>:0 
  at (wrapper remoting-invoke-with-check) System.Diagnostics.Process.Kill()
  at UnityEditor.Android.AndroidDeploymentTargetsExtension.GetKnownTargets (UnityEditor.DeploymentTargets.IDeploymentTargetsMainThreadContext context, UnityEditor.ProgressHandler progressHandler) [0x00174] in <308c1e90026945389c5bf5b88ab3814b>:0 
  at UnityEditor.Android.TargetScanWorker.ScanSync () [0x00049] in <308c1e90026945389c5bf5b88ab3814b>:0 
  at UnityEditor.Android.TargetExtension.OnUsbDevicesChanged (UnityEditor.Hardware.UsbDevice[] usbDevices) [0x00087] in <308c1e90026945389c5bf5b88ab3814b>:0 
  at UnityEditor.Android.TargetExtension.OnLoad () [0x00094] in <308c1e90026945389c5bf5b88ab3814b>:0 
  at UnityEditor.Modules.ModuleManager.InitializePlatformSupportModules () [0x0009d] in <e2ec61eabaea4cfa8892d65208615f30>:0 

Refreshing native plugins compatible for Editor in 123.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  3.539 seconds
Domain Reload Profiling:
	ReloadAssembly (3539ms)
		BeginReloadAssembly (176ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (3185ms)
			LoadAssemblies (172ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (202ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (2848ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2496ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (124ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (156ms)
				ProcessInitializeOnLoadMethodAttributes (69ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Registering precompiled user dll's ...
Registered in 0.014404 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 119.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.714 seconds
Domain Reload Profiling:
	ReloadAssembly (2715ms)
		BeginReloadAssembly (245ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (11ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (2267ms)
			LoadAssemblies (188ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (384ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (115ms)
			SetupLoadedEditorAssemblies (1565ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (119ms)
				BeforeProcessingInitializeOnLoad (109ms)
				ProcessInitializeOnLoadAttributes (1205ms)
				ProcessInitializeOnLoadMethodAttributes (98ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3727 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4150.
Memory consumption went from 248.6 MB to 247.9 MB.
Total: 8.926900 ms (FindLiveObjects: 0.480700 ms CreateObjectMapping: 0.275700 ms MarkObjects: 7.236200 ms  DeleteObjects: 0.931500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 2557.784258 seconds.
  path: Assets/assets/completpnl (1) 1 1.prefab
  artifactKey: Guid(7d1034ee8a738584bb52608e6b984a16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/completpnl (1) 1 1.prefab using Guid(7d1034ee8a738584bb52608e6b984a16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b65bd59e49a594f142c9a99f640cffb') in 0.122128 seconds 
Number of asset objects unloaded after import = 144
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Complete Point.prefab
  artifactKey: Guid(7d2e1e3aee5103a49a0da21fff5e03cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Complete Point.prefab using Guid(7d2e1e3aee5103a49a0da21fff5e03cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b299237baa39648e343752a521293d19') in 0.575404 seconds 
Number of asset objects unloaded after import = 94
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/script/Ui/sample + slicing/sample/level complete.jpg
  artifactKey: Guid(ae8ee9427b26cea4b83b1b4b253755d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/sample/level complete.jpg using Guid(ae8ee9427b26cea4b83b1b4b253755d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cf017b77d062393b5ea717edfe156031') in 0.194541 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/complete/COMPLETE.png
  artifactKey: Guid(dc55ed6e91503164cad155dbc4292d9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/complete/COMPLETE.png using Guid(dc55ed6e91503164cad155dbc4292d9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eba6b9666f9cc04a567ae4757828e134') in 0.042672 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '49f936603e9e501cc4547b4bc405efea') in 0.196115 seconds 
Number of asset objects unloaded after import = 104
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0