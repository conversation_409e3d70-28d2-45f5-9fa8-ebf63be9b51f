using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(linkattacher))]
public class LinkAttacherEditor : Editor
{
    public override void OnInspectorGUI()
    {
        linkattacher script = (linkattacher)target;
        
        // Title
        EditorGUILayout.Space(10);
        GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
        titleStyle.fontSize = 16;
        titleStyle.alignment = TextAnchor.MiddleCenter;
        EditorGUILayout.LabelField("Link Attacher Controller", titleStyle);
        
        EditorGUILayout.Space(15);
        
        // Active Equipment Section
        EditorGUILayout.BeginVertical("box");
        GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel);
        headerStyle.fontSize = 14;
        headerStyle.normal.textColor = Color.green;
        EditorGUILayout.LabelField("🟢 Active Equipment", headerStyle);
        
        EditorGUILayout.Space(5);
        script.grasscutter = (GameObject)EditorGUILayout.ObjectField("Grass Cutter", script.grasscutter, typeof(GameObject), true);
        script.groundflater = (GameObject)EditorGUILayout.ObjectField("Ground Flater", script.groundflater, typeof(GameObject), true);
        script.grasspick = (GameObject)EditorGUILayout.ObjectField("Grass Pick", script.grasspick, typeof(GameObject), true);
        script.saeedmachine = (GameObject)EditorGUILayout.ObjectField("Seed Machine", script.saeedmachine, typeof(GameObject), true);
        script.plough = (GameObject)EditorGUILayout.ObjectField("Plough", script.plough, typeof(GameObject), true);
        EditorGUILayout.EndVertical();
        
        EditorGUILayout.Space(20);
        
        // Inactive Equipment Section
        EditorGUILayout.BeginVertical("box");
        headerStyle.normal.textColor = Color.red;
        EditorGUILayout.LabelField("🔴 Inactive Equipment", headerStyle);
        
        EditorGUILayout.Space(5);
        script.grasscutter1 = (GameObject)EditorGUILayout.ObjectField("Grass Cutter (Inactive)", script.grasscutter1, typeof(GameObject), true);
        script.groundflater1 = (GameObject)EditorGUILayout.ObjectField("Ground Flater (Inactive)", script.groundflater1, typeof(GameObject), true);
        script.grasspick1 = (GameObject)EditorGUILayout.ObjectField("Grass Pick (Inactive)", script.grasspick1, typeof(GameObject), true);
        script.saeedmachine1 = (GameObject)EditorGUILayout.ObjectField("Seed Machine (Inactive)", script.saeedmachine1, typeof(GameObject), true);
        script.plough1 = (GameObject)EditorGUILayout.ObjectField("Plough (Inactive)", script.plough1, typeof(GameObject), true);
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(20);

        // Equipment Check Arrays Section
        EditorGUILayout.BeginVertical("box");
        headerStyle.normal.textColor = Color.blue;
        EditorGUILayout.LabelField("🔧 Equipment Check Arrays", headerStyle);

        EditorGUILayout.Space(5);

        // First Check Array
        EditorGUILayout.LabelField("First Check Objects (to disable):", EditorStyles.boldLabel);
        SerializedProperty firstCheckProp = serializedObject.FindProperty("firstcheck");
        EditorGUILayout.PropertyField(firstCheckProp, true);

        EditorGUILayout.Space(10);

        // Second Check Array
        EditorGUILayout.LabelField("Second Check Objects (to enable):", EditorStyles.boldLabel);
        SerializedProperty secondCheckProp = serializedObject.FindProperty("scondcheck");
        EditorGUILayout.PropertyField(secondCheckProp, true);

        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(10);

        // Help Box
        EditorGUILayout.HelpBox("Assign GameObjects for farming equipment. Active objects will be shown when equipment is attached, inactive objects will be hidden. Use the arrays to control additional objects that should be enabled/disabled during equipment attachment.", MessageType.Info);
        
        // Apply changes
        serializedObject.ApplyModifiedProperties();

        if (GUI.changed)
        {
            EditorUtility.SetDirty(script);
        }
    }
}
