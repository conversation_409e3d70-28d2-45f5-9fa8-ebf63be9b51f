using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    public GameObject[] level;
    public Transform[] Position;
    public GameObject Tractor;
    public int currentLevelIndex = 0;
    void Start()
    {

        LoadLevelProgress();

        InitializeLevels();
    }

    void InitializeLevels()
    {

        for (int i = 0; i < level.Length; i++)
        {
            if (level[i] != null)
            {
                level[i].SetActive(false);
            }
        }


        if (level.Length > 0 && currentLevelIndex < level.Length && level[currentLevelIndex] != null)
        {
            level[currentLevelIndex].SetActive(true);
            
            // Set tractor position when level becomes active
            if (Tractor != null && Position != null && currentLevelIndex < Position.Length && Position[currentLevelIndex] != null)
            {
                Tractor.transform.position = Position[currentLevelIndex].position;
                Tractor.transform.rotation = Position[currentLevelIndex].rotation;
                
                // Set rigidbody interpolation after 1 second
                StartCoroutine(SetTractorInterpolation());
            }
        }
    }

    public void NextLevel()
    {

        if (level.Length > 0 && currentLevelIndex < level.Length - 1)
        {

            if (level[currentLevelIndex] != null)
            {
                level[currentLevelIndex].SetActive(false);
            }


            currentLevelIndex++;


            if (level[currentLevelIndex] != null)
            {
                level[currentLevelIndex].SetActive(true);
                
                // Set tractor position when switching to next level
                if (Tractor != null && Position != null && currentLevelIndex < Position.Length && Position[currentLevelIndex] != null)
                {
                    Tractor.transform.position = Position[currentLevelIndex].position;
                    Tractor.transform.rotation = Position[currentLevelIndex].rotation;
                    
                    // Set rigidbody interpolation after 1 second
                    StartCoroutine(SetTractorInterpolation());
                }
            }


            SaveLevelProgress();


            SceneManager.LoadScene("Farming mod");
        }
    }


    public int GetCurrentLevelIndex()
    {
        return currentLevelIndex;
    }


    public bool HasNextLevel()
    {
        return currentLevelIndex < level.Length - 1;
    }


    void SaveLevelProgress()
    {

        PlayerPrefs.SetInt("CurrentLevelIndex", currentLevelIndex);


        for (int i = 0; i <= currentLevelIndex; i++)
        {
            PlayerPrefs.SetInt("Level_" + i + "_Unlocked", 1);
        }


        PlayerPrefs.Save();
    }


    void LoadLevelProgress()
    {

        currentLevelIndex = PlayerPrefs.GetInt("CurrentLevelIndex", 0);
    }


    public bool IsLevelUnlocked(int levelIndex)
    {
        return PlayerPrefs.GetInt("Level_" + levelIndex + "_Unlocked", 0) == 1;
    }


    public void ResetLevelProgress()
    {
        PlayerPrefs.DeleteKey("CurrentLevelIndex");

        for (int i = 0; i < level.Length; i++)
        {
            PlayerPrefs.DeleteKey("Level_" + i + "_Unlocked");
        }

        currentLevelIndex = 0;
        PlayerPrefs.Save();
        InitializeLevels();
    }

    // Coroutine to set tractor rigidbody interpolation after 1 second
    IEnumerator SetTractorInterpolation()
    {
        // Wait for 1 second
        yield return new WaitForSeconds(1f);
        
        // Set rigidbody interpolation to Interpolate
        if (Tractor != null)
        {
            Rigidbody tractorRigidbody = Tractor.GetComponent<Rigidbody>();
            if (tractorRigidbody != null)
            {
                tractorRigidbody.interpolation = RigidbodyInterpolation.Interpolate;
            }
        }
    }


}
