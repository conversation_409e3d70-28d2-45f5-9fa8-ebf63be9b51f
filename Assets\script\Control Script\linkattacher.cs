using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class linkattacher : MonoBehaviour
{
    [Head<PERSON>("Active Equipment")]
    [Space(10)]
    public GameObject grasscutter, groundflater, grasspick, saeedmachine, plough;

    [Space(20)]
    [Header("Inactive Equipment")]
    [Space(10)]
    public GameObject grasscutter1, groundflater1, grasspick1, saeedmachine1, plough1;

    [Space(20)]
    [<PERSON><PERSON>("Equipment Check Arrays")]
    [Space(10)]
    public GameObject[] firstcheck, scondcheck;

    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.CompareTag("Grasscutter"))
        {
            grasscutter.SetActive(true);
            grasscutter1.SetActive(false);
            this.gameObject.SetActive(false);
            other.gameObject.SetActive(false);
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in scondcheck)
            {
                obj.SetActive(true);
            }

        }
        else if (other.gameObject.CompareTag("Groundflater"))
        {
            groundflater.SetActive(true);
            groundflater1.SetActive(false);
            this.gameObject.SetActive(false);
            other.gameObject.SetActive(false);
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in scondcheck)
            {
                obj.SetActive(true);
            }
        }
        else if (other.gameObject.CompareTag("Grasspicker"))
        {
            grasspick.SetActive(true);
            grasspick1.SetActive(false);
            this.gameObject.SetActive(false);
            other.gameObject.SetActive(false);
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in scondcheck)
            {
                obj.SetActive(true);
            }
        }
        else if (other.gameObject.CompareTag("Saeedmachine"))
        {
            saeedmachine.SetActive(true);
            saeedmachine1.SetActive(false);
            this.gameObject.SetActive(false);
            other.gameObject.SetActive(false);
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in scondcheck)
            {
                obj.SetActive(true);
            }
        }
        else if (other.gameObject.CompareTag("Plough"))
        {
            plough.SetActive(true);
            plough1.SetActive(false);
            this.gameObject.SetActive(false);
            other.gameObject.SetActive(false);
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in scondcheck)
            {
                obj.SetActive(true);
            }
        }



    }
}
